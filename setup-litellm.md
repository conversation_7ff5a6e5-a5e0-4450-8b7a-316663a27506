# LiteLLM Setup for JustPrototype

This guide helps you set up LiteLLM as a proxy for DeepSeek, Qwen, and other LLM providers.

## Quick Setup

### 1. Environment Variables

Add these to your `.env` file:

```bash
# LiteLLM Configuration
LITELLM_BASE_URL=http://localhost:4000
LITELLM_API_KEY=sk-1234

# DeepSeek API Key (Primary provider)
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# OpenRouter API Key (for Qwen models)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Optional: OpenAI (when you have access)
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Anthropic (backup)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

### 2. Start LiteLLM Proxy

```bash
# Using Docker Compose (Recommended)
docker-compose -f docker-compose.litellm.yml up -d

# Or using Python directly
pip install litellm[proxy]
litellm --config backend/litellm_config.yaml --port 4000
```

### 3. Verify Setup

```bash
# Test the proxy
curl http://localhost:4000/health

# Test a model
curl -X POST http://localhost:4000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-1234" \
  -d '{
    "model": "deepseek/deepseek-chat",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

## Model Selection Strategy

Our implementation automatically selects the best model for each task:

| Task Type | Primary Model | Fallback |
|-----------|---------------|----------|
| Intent Analysis | `deepseek/deepseek-reasoner` | `qwen/qwen-2.5-72b-instruct` |
| Planning | `qwen/qwen-2.5-72b-instruct` | `deepseek/deepseek-chat` |
| Code Generation | `deepseek/deepseek-coder-v2` | `gpt-4o` |
| Context Analysis | `qwen/qwen-2.5-32b-instruct` | `deepseek/deepseek-chat` |

## Cost Comparison

- **DeepSeek**: ~$0.14/$1.10 per 1M tokens (input/output)
- **Qwen via OpenRouter**: ~$0.80/$2.40 per 1M tokens
- **GPT-4o**: ~$2.50/$10.00 per 1M tokens

## Getting API Keys

### DeepSeek
1. Visit https://platform.deepseek.com/
2. Sign up and get API key
3. Very cost-effective for coding tasks

### OpenRouter (for Qwen)
1. Visit https://openrouter.ai/
2. Sign up and get API key
3. Access to many models including Qwen

## Troubleshooting

### LiteLLM not starting
- Check Docker is running
- Verify config file path
- Check API keys are set

### Model not responding
- Check API key validity
- Verify model name in config
- Check LiteLLM logs: `docker-compose -f docker-compose.litellm.yml logs`

### High latency
- Use smaller models for faster responses
- Consider local deployment for frequently used models
