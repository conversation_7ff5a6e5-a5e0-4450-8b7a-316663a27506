# LiteLLM Proxy Server

A production-ready LiteLLM proxy server for cost-effective AI model access. Supports DeepSeek, Qwen (via OpenRouter), OpenAI, and Anthropic models with intelligent routing and fallback strategies.

## 🚀 Features

- **Cost Optimization**: 85% cost reduction using DeepSeek vs GPT-4o
- **Multi-Provider Support**: DeepSeek, OpenRouter, OpenAI, Anthropic
- **Intelligent Routing**: Task-specific model selection
- **Automatic Fallbacks**: Seamless provider switching
- **Production Ready**: Docker, Railway, and local deployment options

## 💰 Cost Comparison

| Provider | Model | Input (1M tokens) | Output (1M tokens) | Use Case |
|----------|-------|-------------------|-------------------|----------|
| **DeepSeek** | deepseek-chat | $0.14 | $1.10 | General tasks |
| **DeepSeek** | deepseek-coder | $0.14 | $1.10 | Code generation |
| **Qwen** | qwen-2.5-72b | $0.80 | $2.40 | Complex reasoning |
| **OpenAI** | gpt-4o | $2.50 | $10.00 | Fallback |

## 🏗️ Deployment Options

### Option 1: Railway (Recommended)

## Quick Deployment Steps

### 1. Create New Railway Project
1. Go to https://railway.app/
2. Sign in with GitHub
3. Click "New Project"
4. Choose "Deploy from GitHub repo"

### 2. Upload Files
Create a new GitHub repository with these files:
- `Dockerfile`
- `litellm_config.yaml`
- `railway.json`

### 3. Set Environment Variables
In Railway dashboard, go to Variables tab and add:

```
DEEPSEEK_API_KEY=sk1-9009ee66fcfd45ee80e2390da604331e
OPENROUTER_API_KEY=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
OPENAI_API_KEY=********************************************************************************************************************************************************************
ANTHROPIC_API_KEY=************************************************************************************************************
LITELLM_MASTER_KEY=sk-railway-litellm-2024
PORT=4000
```

### 4. Deploy
1. Railway will automatically build and deploy
2. You'll get a URL like: `https://your-app-name.railway.app`
3. Test health: `https://your-app-name.railway.app/health`

### 5. Update Your Backend
Update `backend/.env`:
```
LITELLM_BASE_URL=https://your-app-name.railway.app
LITELLM_API_KEY=sk-railway-litellm-2024
```

## Alternative: Use Direct APIs (Recommended)

Since you already have the API keys, I recommend using the direct API approach I just implemented. This is simpler and more reliable:

### Benefits:
✅ No deployment needed
✅ No additional infrastructure
✅ Same cost savings (DeepSeek ~85% cheaper)
✅ Better reliability
✅ Easier to debug

### Test Direct API Setup:
1. Your backend is already configured for direct APIs
2. Start backend: `cd backend && npm start`
3. It will automatically use DeepSeek (cheapest) → OpenRouter → OpenAI → Anthropic

## Cost Comparison:
- **DeepSeek Direct**: $0.14/$1.10 per 1M tokens
- **Railway LiteLLM**: $0.14/$1.10 + Railway hosting (~$5/month)
- **GPT-4o**: $2.50/$10.00 per 1M tokens

### Option 2: Local Docker

1. **Clone this repository**
```bash
git clone https://github.com/sanjeev23oct/litellm.git
cd litellm
```

2. **Set up environment**
```bash
cp .env.example .env
# Edit .env with your API keys
```

3. **Start with Docker Compose**
```bash
docker-compose up -d
```

4. **Test the setup**
```bash
curl http://localhost:4000/health
```

### Option 3: Manual Docker

```bash
# Build the image
docker build -t my-litellm .

# Run the container
docker run -d \
  -p 4000:4000 \
  -e DEEPSEEK_API_KEY=your_key \
  -e OPENROUTER_API_KEY=your_key \
  -e LITELLM_MASTER_KEY=your_master_key \
  my-litellm
```

## 🧪 Testing

### Health Check
```bash
curl https://your-app.railway.app/health
```

### Test Model
```bash
curl -X POST https://your-app.railway.app/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_master_key" \
  -d '{
    "model": "deepseek-chat",
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 100
  }'
```

### Available Models
```bash
curl -H "Authorization: Bearer your_master_key" \
  https://your-app.railway.app/v1/models
```

## 🔧 Configuration

The `litellm_config.yaml` file defines:
- Available models and their API endpoints
- Routing strategies
- Rate limiting
- Logging settings

### Model Groups
- **reasoning**: `deepseek-reasoner`, `qwen/qwen-2.5-72b-instruct`
- **coding**: `deepseek-coder`, `qwen/qwen-2.5-coder-32b-instruct`
- **general**: `deepseek-chat`, `qwen/qwen-2.5-32b-instruct`

## 📊 Monitoring

### Logs
```bash
# Railway
railway logs

# Docker Compose
docker-compose logs -f

# Docker
docker logs container_name
```

### Metrics
- Response times by provider
- Cost tracking per request
- Error rates and fallbacks

## 🔐 Security

- API keys stored as environment variables
- Master key authentication required
- Rate limiting enabled
- Request logging for audit

## 🚨 Troubleshooting

### Common Issues

1. **Container won't start**
   - Check API keys are set
   - Verify config file syntax
   - Check port availability

2. **Model not responding**
   - Verify API key validity
   - Check model name spelling
   - Review provider status

3. **High costs**
   - Monitor usage patterns
   - Adjust routing strategy
   - Set budget limits

## 📝 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

- GitHub Issues: Report bugs and feature requests
- Documentation: Check the LiteLLM official docs
- Community: Join the LiteLLM Discord

## Recommendation:
This LiteLLM proxy is perfect for production use when you need centralized model management, logging, and load balancing. For simple applications, direct API calls might be sufficient.
