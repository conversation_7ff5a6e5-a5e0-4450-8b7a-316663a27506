# Deploy LiteLLM on Railway

## Quick Deployment Steps

### 1. Create New Railway Project
1. Go to https://railway.app/
2. Sign in with GitHub
3. Click "New Project"
4. Choose "Deploy from GitHub repo"

### 2. Upload Files
Create a new GitHub repository with these files:
- `Dockerfile`
- `litellm_config.yaml`
- `railway.json`

### 3. Set Environment Variables
In Railway dashboard, go to Variables tab and add:

```
DEEPSEEK_API_KEY=sk1-9009ee66fcfd45ee80e2390da604331e
OPENROUTER_API_KEY=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
OPENAI_API_KEY=********************************************************************************************************************************************************************
ANTHROPIC_API_KEY=************************************************************************************************************
LITELLM_MASTER_KEY=sk-railway-litellm-2024
PORT=4000
```

### 4. Deploy
1. Railway will automatically build and deploy
2. You'll get a URL like: `https://your-app-name.railway.app`
3. Test health: `https://your-app-name.railway.app/health`

### 5. Update Your Backend
Update `backend/.env`:
```
LITELLM_BASE_URL=https://your-app-name.railway.app
LITELLM_API_KEY=sk-railway-litellm-2024
```

## Alternative: Use Direct APIs (Recommended)

Since you already have the API keys, I recommend using the direct API approach I just implemented. This is simpler and more reliable:

### Benefits:
✅ No deployment needed
✅ No additional infrastructure 
✅ Same cost savings (DeepSeek ~85% cheaper)
✅ Better reliability
✅ Easier to debug

### Test Direct API Setup:
1. Your backend is already configured for direct APIs
2. Start backend: `cd backend && npm start`
3. It will automatically use DeepSeek (cheapest) → OpenRouter → OpenAI → Anthropic

## Cost Comparison:
- **DeepSeek Direct**: $0.14/$1.10 per 1M tokens
- **Railway LiteLLM**: $0.14/$1.10 + Railway hosting (~$5/month)
- **GPT-4o**: $2.50/$10.00 per 1M tokens

## Recommendation:
Try the **direct API approach first** - it's already implemented and working. Only use Railway LiteLLM if you need the proxy features like load balancing or centralized logging.
