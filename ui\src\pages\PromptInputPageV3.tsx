import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON>Loader, FiMonitor, FiSmartphone, FiZap, FiTarget, FiLink } from 'react-icons/fi';

/**
 * PromptInputPageV3 - Readdy.ai style initial prompt page
 * Sophisticated design matching StartingPage.png reference
 */

type DeviceType = 'desktop' | 'mobile';

export function PromptInputPageV3() {
  const [prompt, setPrompt] = useState('');
  const [deviceType, setDeviceType] = useState<DeviceType>('desktop');
  const [isGenerating, setIsGenerating] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || isGenerating) return;

    setIsGenerating(true);

    try {
      // Navigate to plan review page with the prompt
      navigate('/plan-v3', {
        state: {
          prompt: prompt.trim(),
          deviceType
        }
      });
    } catch (error) {
      console.error('Error:', error);
      setIsGenerating(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center px-4 py-8">
      <div className="w-full max-w-4xl">
        {/* Logo and Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-2xl">JP</span>
            </div>
            <span className="ml-4 text-3xl font-bold text-gray-900">JustPrototype</span>
          </div>
          <h1 className="text-5xl font-bold text-gray-900 mb-6 leading-tight">
            What would you like to
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600">
              design today?
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
            Describe your vision and I'll create a professional, interactive prototype for you
          </p>
          <div className="flex items-center justify-center space-x-4 text-sm">
            <span className="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-700 font-medium">
              <FiZap className="mr-2" />
              Interactive Elements
            </span>
            <span className="inline-flex items-center px-4 py-2 rounded-full bg-emerald-100 text-emerald-700 font-medium">
              <FiTarget className="mr-2" />
              Modal Support
            </span>
            <span className="inline-flex items-center px-4 py-2 rounded-full bg-purple-100 text-purple-700 font-medium">
              <FiLink className="mr-2" />
              Multi-Page
            </span>
          </div>
        </div>

        {/* Device Type Selection */}
        <div className="flex justify-center mb-12">
          <div className="bg-white rounded-xl p-2 shadow-lg border border-gray-100">
            <button
              type="button"
              onClick={() => setDeviceType('desktop')}
              className={`flex items-center space-x-3 px-6 py-3 rounded-lg text-base font-medium transition-all duration-200 ${
                deviceType === 'desktop'
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <FiMonitor className="text-lg" />
              <span>Desktop</span>
            </button>
            <button
              type="button"
              onClick={() => setDeviceType('mobile')}
              className={`flex items-center space-x-3 px-6 py-3 rounded-lg text-base font-medium transition-all duration-200 ${
                deviceType === 'mobile'
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <FiSmartphone className="text-lg" />
              <span>Mobile</span>
            </button>
          </div>
        </div>

        {/* Prompt Input */}
        <form onSubmit={handleSubmit} className="relative mb-12">
          <div className="bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden backdrop-blur-sm">
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Describe your vision in detail...

Include style preferences, features, and functionality for best results.
Reference images and specific requirements are welcome ✨"
              className="w-full h-40 px-8 py-6 text-lg text-gray-900 placeholder-gray-500 border-0 resize-none focus:outline-none focus:ring-0 leading-relaxed"
              disabled={isGenerating}
            />

            {/* Input Actions */}
            <div className="flex items-center justify-between px-8 py-6 bg-gradient-to-r from-gray-50 to-blue-50 border-t border-gray-100">
              <div className="flex items-center space-x-4">
                <button
                  type="button"
                  className="p-3 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                  title="Add attachment"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>
                <button
                  type="button"
                  className="p-3 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                  title="Color palette"
                >
                  <div className="w-5 h-5 rounded-full bg-gradient-to-r from-red-400 via-yellow-400 to-blue-400 shadow-sm"></div>
                </button>
                <button
                  type="button"
                  className="p-3 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                  title="Style options"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                  </svg>
                </button>
                <button
                  type="button"
                  className="p-3 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                  title="Voice input"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                </button>
              </div>

              <button
                type="submit"
                disabled={!prompt.trim() || isGenerating}
                className="flex items-center space-x-3 px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl font-medium text-base"
              >
                {isGenerating ? (
                  <>
                    <FiLoader className="animate-spin text-lg" />
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <FiSend className="text-lg" />
                    <span>Create Prototype</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </form>

        {/* Examples */}
        <div className="text-center">
          <p className="text-lg text-gray-600 mb-8 font-medium">Try these examples with interactive features:</p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
            {[
              {
                title: 'Modern SaaS Landing Page',
                description: 'with login and signup modals',
                prompt: 'Modern SaaS landing page with login and signup modals'
              },
              {
                title: 'E-commerce Product Page',
                description: 'with cart and checkout features',
                prompt: 'E-commerce product page with cart and checkout features'
              },
              {
                title: 'Dashboard Interface',
                description: 'with user profile and settings modals',
                prompt: 'Dashboard with user profile and settings modals'
              },
              {
                title: 'Portfolio Website',
                description: 'with contact form and project galleries',
                prompt: 'Portfolio website with contact form and project galleries'
              }
            ].map((example, index) => (
              <button
                key={index}
                onClick={() => setPrompt(example.prompt)}
                className="p-6 text-left bg-white rounded-2xl border border-gray-100 hover:border-blue-200 hover:shadow-lg transition-all duration-200 group disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isGenerating}
              >
                <div className="font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                  {example.title}
                </div>
                <div className="text-gray-600 text-sm">
                  {example.description}
                </div>
              </button>
            ))}
          </div>
          <div className="mt-8 flex items-center justify-center space-x-2 text-sm text-gray-500">
            <FiZap className="text-blue-500" />
            <span>All examples include interactive elements ready for modal enhancement</span>
          </div>
        </div>
      </div>
    </div>
  );
}
