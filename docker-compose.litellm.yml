version: '3.8'

services:
  litellm:
    image: ghcr.io/berriai/litellm:main-latest
    ports:
      - "4000:4000"
    volumes:
      - ./backend/litellm_config.yaml:/app/config.yaml
    environment:
      # DeepSeek API Key
      - DEEPSEEK_API_KEY=sk1-9009ee66fcfd45ee80e2390da604331e

      # OpenRouter API Key (for Qwen models)
      - OPENROUTER_API_KEY=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

      # OpenAI API Key (optional, when available)
      - OPENAI_API_KEY=********************************************************************************************************************************************************************

      # Anthropic API Key (backup)
      - ANTHROPIC_API_KEY=************************************************************************************************************

      # LiteLLM settings
      - LITELLM_MASTER_KEY=sk-1234
      - LITELLM_LOG=INFO

    command: ["--config", "/app/config.yaml", "--port", "4000", "--num_workers", "1"]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
