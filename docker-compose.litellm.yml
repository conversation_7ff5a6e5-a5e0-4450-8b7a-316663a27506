version: '3.8'

services:
  litellm:
    image: ghcr.io/berriai/litellm:main-latest
    ports:
      - "4000:4000"
    volumes:
      - ./backend/litellm_config.yaml:/app/config.yaml
    environment:
      # DeepSeek API Key
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      
      # OpenRouter API Key (for Qwen models)
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      
      # OpenAI API Key (optional, when available)
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      
      # Anthropic API Key (backup)
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      
      # LiteLLM settings
      - LITELLM_MASTER_KEY=sk-1234
      - LITELLM_LOG=INFO
      
    command: ["--config", "/app/config.yaml", "--port", "4000", "--num_workers", "1"]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
