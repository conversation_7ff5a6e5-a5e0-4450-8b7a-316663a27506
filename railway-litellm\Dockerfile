# Use official LiteLLM image as base
FROM ghcr.io/berriai/litellm:main-latest

# Set working directory
WORKDIR /app

# Copy configuration file
COPY litellm_config.yaml /app/config.yaml

# Expose port (Railway will set PORT env var)
EXPOSE 4000

# Set environment variables
ENV LITELLM_LOG=INFO

# Start LiteLLM with config
# Note: Railway will provide PORT via environment variable
CMD ["--config", "/app/config.yaml", "--port", "4000", "--num_workers", "1"]
