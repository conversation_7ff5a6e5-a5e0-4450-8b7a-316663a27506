const { ChatOpenAI } = require('@langchain/openai');
const { ChatAnthropic } = require('@langchain/anthropic');
const { HumanMessage, SystemMessage } = require('@langchain/core/messages');

// System prompts for different tasks
const systemPrompts = {
  intent: `You are a UI/UX expert. Analyze HTML elements and determine what functionality should be implemented when users interact with them.

Return a JSON response with:
- canGenerate: boolean (whether functionality can be generated)
- userIntent: string (what the user likely wants to achieve)
- suggestion: string (detailed implementation suggestion)

Focus on common UI patterns and user expectations.`,

  modification: `You are an elite frontend developer specializing in precise HTML modifications while maintaining production-grade quality.

CRITICAL INSTRUCTIONS:
1. Make ONLY the changes requested in the prompt
2. Preserve ALL existing functionality, styling, and structure
3. Maintain the exact same design system and patterns
4. Keep all unrelated elements completely unchanged
5. If adding new elements, follow existing patterns in the document
6. Ensure changes are contextually appropriate and well-integrated
7. Maintain clean, properly formatted code structure
8. Preserve consistent naming conventions and CSS organization
9. Keep the same visual design language and component styling

Return the complete modified HTML document without markdown formatting. Ensure the output maintains the same professional quality and code structure as the original.`,

  code: `You are an expert web developer. Create complete, modern, responsive HTML pages with embedded CSS and JavaScript.

REQUIREMENTS:
- Use modern HTML5 semantic elements
- Include responsive design with CSS Grid/Flexbox
- Add interactive JavaScript functionality
- Use modern CSS features (custom properties, etc.)
- Ensure accessibility (ARIA labels, semantic HTML)
- Include proper meta tags and viewport settings
- Use a cohesive design system with consistent colors and typography

Return only the complete HTML document without markdown formatting.`
};

/**
 * V3 LLM Service - Clean implementation based on Readdy.ai approach
 * Focuses on sophisticated prompting and context management for accurate editing
 */

class LLMServiceV3 {
  constructor() {
    this.providers = {
      openai: process.env.OPENAI_API_KEY,
      anthropic: process.env.ANTHROPIC_API_KEY,
      litellm: process.env.LITELLM_API_KEY || 'sk-1234', // LiteLLM proxy often uses dummy key
      deepseek: process.env.DEEPSEEK_API_KEY,
      openrouter: process.env.OPENROUTER_API_KEY
    };

    // LiteLLM proxy configuration
    this.litellmConfig = {
      baseURL: process.env.LITELLM_BASE_URL || 'http://localhost:4000', // Default LiteLLM proxy URL
      apiKey: this.providers.litellm
    };

    // Model mapping for different providers and tasks
    this.modelMapping = {
      // OpenAI models (when available)
      openai: {
        'intent-analysis': 'gpt-4o', // Use gpt-4o instead of o1 for now
        'planning': 'gpt-4o',
        'code-generation': 'gpt-4o',
        'context-analysis': 'gpt-4o',
        'general': 'gpt-4o'
      },
      // LiteLLM proxy models (DeepSeek, Qwen, etc.)
      litellm: {
        'intent-analysis': 'deepseek/deepseek-reasoner', // DeepSeek reasoning model
        'planning': 'qwen/qwen-2.5-72b-instruct', // Qwen for planning
        'code-generation': 'deepseek/deepseek-coder-v2', // DeepSeek coder
        'context-analysis': 'qwen/qwen-2.5-32b-instruct', // Qwen for context
        'general': 'deepseek/deepseek-chat'
      },
      // Anthropic models
      anthropic: {
        'intent-analysis': 'claude-3-5-sonnet-20241022',
        'planning': 'claude-3-5-sonnet-20241022',
        'code-generation': 'claude-3-5-sonnet-20241022',
        'context-analysis': 'claude-3-5-sonnet-20241022',
        'general': 'claude-3-5-sonnet-20241022'
      }
    };
  }

  /**
   * Get the best available provider based on configuration
   */
  getBestProvider() {
    // Priority order: LiteLLM (for cost-effective alternatives) > OpenAI > Anthropic
    if (this.providers.litellm && process.env.LITELLM_BASE_URL) {
      return 'litellm';
    }
    if (this.providers.openai) {
      return 'openai';
    }
    if (this.providers.anthropic) {
      return 'anthropic';
    }
    throw new Error('No LLM provider configured. Please set up LiteLLM, OpenAI, or Anthropic.');
  }

  /**
   * Create LLM instance with intelligent model selection based on task
   */
  createLLM(provider = null, streaming = true, taskType = 'general') {
    // Auto-select best provider if not specified
    const selectedProvider = provider || this.getBestProvider();
    const providerKey = selectedProvider.toLowerCase();

    // Get the appropriate model for this task and provider
    const modelName = this.modelMapping[providerKey]?.[taskType] || this.modelMapping[providerKey]?.['general'];

    if (!modelName) {
      throw new Error(`No model mapping found for provider ${providerKey} and task ${taskType}`);
    }

    console.log(`🤖 Using ${providerKey} provider with model ${modelName} for task: ${taskType}`);

    switch (providerKey) {
      case 'openai':
        if (!this.providers.openai) {
          throw new Error('OpenAI API key not configured');
        }

        return new ChatOpenAI({
          apiKey: this.providers.openai,
          modelName: modelName,
          temperature: 0.7,
          streaming: streaming
        });

      case 'litellm':
        // Use LiteLLM proxy with OpenAI-compatible interface
        return new ChatOpenAI({
          apiKey: this.litellmConfig.apiKey,
          modelName: modelName,
          temperature: 0.7,
          streaming: streaming,
          configuration: {
            baseURL: this.litellmConfig.baseURL + '/v1' // LiteLLM proxy endpoint
          }
        });

      case 'anthropic':
        if (!this.providers.anthropic) {
          throw new Error('Anthropic API key not configured');
        }

        return new ChatAnthropic({
          apiKey: this.providers.anthropic,
          modelName: modelName,
          temperature: 0.7,
          streaming: streaming
        });

      default:
        throw new Error(`Unsupported provider: ${providerKey}`);
    }
  }

  /**
   * Send SSE event
   */
  sendSSEEvent(res, event, data) {
    res.write(`event:${event}\n`);
    res.write(`data:${data}\n\n`);
  }

  /**
   * Generate intent from element code (Readdy.ai approach)
   */
  async generateIntent(elementCode) {
    const llm = this.createLLM('openai', false);

    const systemPrompt = `You are a UI/UX expert. Analyze HTML elements and determine what functionality should be implemented when users interact with them.

Return a JSON response with:
- canGenerate: boolean (whether functionality can be generated)
- userIntent: string (what the user likely wants to achieve)
- suggestion: string (detailed implementation suggestion)

Focus on common UI patterns and user expectations.`;

    const userPrompt = `Analyze this UI element:

${elementCode}

What functionality should be implemented when a user interacts with this element?`;

    const messages = [
      new SystemMessage(systemPrompt),
      new HumanMessage(userPrompt)
    ];

    try {
      const response = await llm.invoke(messages);
      return JSON.parse(response.content);
    } catch (error) {
      console.error('Error generating intent:', error);
      return {
        canGenerate: false,
        userIntent: 'Unable to determine user intent',
        suggestion: 'Please provide more context or try a different approach'
      };
    }
  }

  /**
   * Generate complete HTML from prompt
   */
  async generateHTML(prompt, res, provider = null) {
    console.log('🎯 [LLMServiceV3] generateHTML called');
    console.log('📏 Prompt length:', prompt.length);
    console.log('🔍 Prompt preview:', prompt.substring(0, 200) + '...');
    console.log('🔍 Prompt contains plan data:', prompt.includes('DETAILED IMPLEMENTATION PLAN'));

    const llm = this.createLLM(provider, true, 'code-generation'); // Use code generation model

    const systemPrompt = `You are an elite frontend developer creating production-grade HTML prototypes. Generate complete, functional web interfaces that implement ALL requested features comprehensively.

## CRITICAL REQUIREMENTS:

### Feature Implementation:
- Implement EVERY feature mentioned in the prompt completely
- Add functional JavaScript for all interactive elements
- Include working forms, buttons, navigation, and dynamic content
- Create realistic data and content for demonstrations
- Ensure all components are fully functional, not just visual

### Code Quality Standards:
- Write clean, semantic HTML5 with proper document structure
- Use modern CSS with custom properties (CSS variables)
- Implement responsive design with mobile-first approach
- Follow accessibility best practices (ARIA labels, semantic elements)
- Use consistent naming conventions (BEM methodology preferred)
- Include proper meta tags and viewport configuration

### Design System:
- Typography: Use system fonts with proper hierarchy (2.5rem, 2rem, 1.5rem, 1.25rem, 1rem)
- Colors: Professional palette with primary (#2563eb), secondary (#10b981), neutral grays
- Spacing: Consistent 8px grid system (0.5rem, 1rem, 1.5rem, 2rem, 3rem, 4rem)
- Components: Clean buttons, cards, forms with subtle shadows and proper states
- Layout: CSS Grid for page structure, Flexbox for components

### Functionality Requirements:
- Add working JavaScript for all interactive features
- Include form validation and submission handling
- Implement navigation and routing where applicable
- Add realistic sample data and content
- Create working animations and transitions
- Include error handling and user feedback

### Code Structure:
- Properly formatted and indented HTML
- Organized CSS with logical grouping (reset, variables, layout, components)
- Comprehensive JavaScript for all interactions
- Comments for complex sections
- No external dependencies unless absolutely necessary

### Visual Excellence:
- Modern, clean aesthetic with generous whitespace
- Subtle animations and hover effects
- Professional color schemes and typography
- Consistent component styling
- Mobile-responsive design

CRITICAL: Return ONLY the complete HTML document with embedded CSS and JavaScript.

DO NOT include:
- Any explanatory text
- Any comments about the code
- Any instructions to the user
- Any markdown formatting
- Any text outside the HTML document

Return ONLY clean HTML code that starts with <!DOCTYPE html> and ends with </html>. No other text whatsoever.`;

    const messages = [
      new SystemMessage(systemPrompt),
      new HumanMessage(prompt)
    ];

    try {
      this.sendSSEEvent(res, 'start', 'Starting HTML generation...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      this.sendSSEEvent(res, 'end', 'HTML generation completed');
      res.end();
    } catch (error) {
      console.error('Error generating HTML:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Analyze user intent and provide contextual understanding (like EditUI.png reference)
   */
  async analyzeAndProvideContext(htmlContent, prompt, res, provider = null, conversationHistory = []) {
    const llm = this.createLLM(provider, false, 'context-analysis'); // Use context analysis model

    // Build conversation context like Readdy does
    const conversationContext = conversationHistory.length > 0
      ? `\n\nCONVERSATION HISTORY:\n${conversationHistory.map(msg => `${msg.role.toUpperCase()}: ${msg.content}`).join('\n\n')}`
      : '';

    const analysisPrompt = `You are an expert UX analyst. Analyze the user's request, current webpage, and conversation history to provide intelligent contextual understanding.

CURRENT WEBPAGE CONTENT:
${htmlContent}

USER REQUEST: ${prompt}${conversationContext}

Based on the conversation history and current context, analyze what the user likely wants to achieve and provide a contextual understanding message that shows you understand their intent.

Examples:
- If user clicked "Generate Report" → "User clicked on Generate Report in the company details section. They likely want to create a comprehensive report about the company's performance, metrics, or data analysis."
- If user clicked "Contact Us" → "User clicked on Contact Us. They likely want to implement a contact form or modal with fields for name, email, and message."
- If user clicked "Sign Up" → "User clicked on Sign Up. They likely want to create a registration form with user details and validation."

Provide a brief, intelligent analysis of what the user wants to accomplish. Be specific about the context and likely implementation needs. Consider the conversation history to understand the full context.

Return ONLY the contextual understanding message. No other text.`;

    try {
      const response = await llm.invoke([new SystemMessage(''), new HumanMessage(analysisPrompt)]);
      const contextMessage = response.content.trim();

      // Send the contextual understanding as a separate event
      this.sendSSEEvent(res, 'context', contextMessage);

    } catch (error) {
      console.error('Error analyzing context:', error);
      // Continue with edit even if context analysis fails
    }
  }

  /**
   * Step 1: Generate Intent (like Readdy's /api/page_gen/generate_intent)
   * Uses reasoning model for better contextual understanding
   */
  async generateIntent(elementCode, htmlContent, conversationHistory = []) {
    // Use reasoning model for intent analysis - server decides model automatically
    const llm = this.createLLM('openai', false, 'intent-analysis');

    const conversationContext = conversationHistory.length > 0
      ? `\n\nCONVERSATION HISTORY:\n${conversationHistory.map(msg => `${msg.role.toUpperCase()}: ${msg.content}`).join('\n\n')}`
      : '';

    const intentPrompt = `You are an expert UX analyst. Analyze the user's click on a UI element and determine their intent.

CURRENT WEBPAGE CONTENT:
${htmlContent}

CLICKED ELEMENT:
${elementCode}${conversationContext}

Analyze what the user likely wants to achieve and provide a detailed suggestion for implementation.

Provide a JSON response with exactly this structure:
{
  "canGenerate": true,
  "userIntent": "The user clicked the [element] in the [context section]. This suggests they [specific goal and reasoning].",
  "suggestion": "When the user clicks the [element], [detailed implementation suggestion with specific features, UI components, and functionality]."
}

Examples:
- Button "Get Support" → userIntent: "The user clicked the 'Get Support' button in the welcome banner section. This suggests they need customer support or assistance with their banking services but the current page doesn't have a support dialog implementation."
- Button "Generate Report" → userIntent: "The user clicked 'Generate Report' in the company details section. They likely want to create a comprehensive report about company performance, metrics, or data analysis."

The suggestion should be detailed and specific about what UI components and functionality should be implemented.

Return ONLY valid JSON. No other text.`;

    try {
      const response = await llm.invoke([new SystemMessage(''), new HumanMessage(intentPrompt)]);
      const content = response.content.trim();

      try {
        const intent = JSON.parse(content);
        return { success: true, intent };
      } catch (parseError) {
        console.log('Failed to parse JSON, using fallback');
        return {
          success: true,
          intent: {
            canGenerate: true,
            userIntent: `User clicked on an interactive element. They likely want to implement functionality for this component.`,
            suggestion: `Implement appropriate functionality for this element with proper user interface and interaction patterns.`
          }
        };
      }
    } catch (error) {
      console.error('Error generating intent:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Edit existing HTML with targeted changes (Readdy.ai approach)
   */
  async editHTML(htmlContent, prompt, res, provider = null, elementSelector = null, conversationHistory = []) {
    const llm = this.createLLM(provider, true, 'code-generation'); // Use code generation model

    // First, analyze the user's intent and provide contextual understanding
    await this.analyzeAndProvideContext(htmlContent, prompt, res, provider, conversationHistory);

    const systemPrompt = `You are an expert web developer specializing in precise HTML modifications. You excel at making targeted changes while preserving existing functionality.

CRITICAL INSTRUCTIONS:
1. Make ONLY the changes requested in the prompt
2. Preserve ALL existing functionality, styling, and structure
3. Maintain the exact same design system and patterns
4. Keep all unrelated elements completely unchanged
5. If adding new elements, follow existing patterns in the document
6. Ensure changes are contextually appropriate and well-integrated
7. If adding new features, implement them completely with working JavaScript
8. Maintain consistent code quality and formatting
9. Preserve all existing CSS variables, classes, and styling
10. Test that all existing functionality still works after changes

MODAL IMPLEMENTATION REQUIREMENTS (if creating modals):
- Add onclick="openModal('modalId')" directly to the button element
- Create modal HTML structure with unique ID using this EXACT pattern:
  <div id="modalId" class="hidden fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg w-full max-w-lg mx-4">
      <div class="p-6 border-b border-gray-100">
        <div class="flex justify-between items-center">
          <h3 class="text-xl font-semibold">Modal Title</h3>
          <button onclick="closeModal('modalId')" class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100">
            <i class="ri-close-line text-gray-500"></i>
          </button>
        </div>
      </div>
      <div class="p-6">
        <!-- Modal content here -->
      </div>
    </div>
  </div>

- Add these EXACT JavaScript functions:
  function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
  }
  function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
  }
  // ESC key listener
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      document.querySelectorAll('.modal:not(.hidden)').forEach(modal => modal.classList.add('hidden'));
    }
  });

- Use vanilla JavaScript only (no external libraries required)
- Follow the Readdy.ai modal pattern exactly

JAVASCRIPT IMPLEMENTATION STANDARDS:
- Use vanilla JavaScript only (no jQuery or external libraries)
- Add event handlers directly as onclick attributes for reliability
- Include complete function definitions in <script> tags
- Test all functionality before returning code
- Ensure immediate functionality after implementation

MODIFICATION APPROACH:
- Analyze the existing code structure and patterns
- Identify the specific area that needs modification
- Make precise, surgical changes to that area only
- Ensure new code follows the same patterns as existing code
- Maintain all existing functionality and styling
- Add comprehensive functionality for any new features

CRITICAL: Return ONLY the complete modified HTML document.

DO NOT include:
- Any explanatory text
- Any comments about changes made
- Any instructions to the user
- Any markdown formatting
- Any text outside the HTML document

Return ONLY clean HTML code that starts with <!DOCTYPE html> and ends with </html>. No other text whatsoever.`;

    let userPrompt = `CURRENT HTML DOCUMENT:
${htmlContent}

REQUESTED CHANGE: ${prompt}`;

    if (elementSelector) {
      userPrompt += `\n\nTARGET ELEMENT SELECTOR: ${elementSelector}
Focus changes on this specific element while preserving everything else.`;
    }

    const messages = [
      new SystemMessage(systemPrompt),
      new HumanMessage(userPrompt)
    ];

    try {
      this.sendSSEEvent(res, 'start', 'Starting HTML editing...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      this.sendSSEEvent(res, 'end', 'HTML editing completed');
      res.end();
    } catch (error) {
      console.error('Error editing HTML:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Generate structured plan from prompt (for plan review page)
   */
  async generateStructuredPlan(prompt, deviceType = 'desktop', provider = null) {
    try {
      const llm = this.createLLM(provider, false, 'planning'); // Use planning model

      const systemPrompt = `You are a senior web developer and UI/UX designer. Create detailed implementation plans for web applications.

Create a comprehensive plan that includes:
- Project overview describing the overall approach
- Implementation sections with specific details
- Key features and functionality
- Accessibility considerations

Focus on modern web design principles, responsive design, and user experience.`;

      const userPrompt = `Create a detailed implementation plan for: "${prompt}"

Device Type: ${deviceType}

Provide a comprehensive plan that covers layout, components, features, and accessibility considerations.`;

      const messages = [
        new SystemMessage(systemPrompt),
        new HumanMessage(userPrompt)
      ];

      const response = await llm.invoke(messages);
      const content = response.content;

      // Parse the response and structure it
      const lines = content.split('\n').filter(line => line.trim());

      // Extract overview (first meaningful paragraph)
      const overview = lines.find(line =>
        line.length > 50 &&
        !line.startsWith('#') &&
        !line.startsWith('-') &&
        !line.startsWith('•')
      ) || "A comprehensive web application design focused on user experience and modern design principles.";

      // Extract sections from the response
      const sections = [
        {
          title: "Layout",
          description: "Overall page structure and arrangement",
          details: [
            "Responsive design that works across all devices",
            "Clean visual hierarchy with proper spacing",
            "Intuitive navigation structure"
          ]
        },
        {
          title: "Components",
          description: "Key interface elements and functionality",
          details: [
            "Interactive elements with clear feedback",
            "Form validation and user input handling",
            "Dynamic content updates"
          ]
        },
        {
          title: "Styling",
          description: "Visual design and user experience",
          details: [
            "Modern CSS styling with consistent branding",
            "Smooth animations and transitions",
            "Accessible color schemes and typography"
          ]
        }
      ];

      return {
        success: true,
        plan: {
          overview,
          sections,
          features: [
            "Responsive design",
            "Modern styling",
            "Interactive elements",
            "Form validation",
            "Accessibility features"
          ],
          accessibility: [
            "Semantic HTML structure",
            "Keyboard navigation support",
            "Screen reader compatibility",
            "WCAG 2.1 compliance"
          ]
        }
      };
    } catch (error) {
      console.error('Error generating structured plan:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate plan from prompt (streaming for chat)
   */
  async generatePlan(prompt, res, provider = null) {
    const llm = this.createLLM(provider, true, 'planning'); // Use planning model

    const systemPrompt = `You are a senior web developer and UI/UX designer. Create detailed implementation plans for web applications.

Your plan should include:
1. Overall structure and layout
2. Key components and features
3. User interface design approach
4. Interactive elements and functionality
5. Technical considerations
6. Implementation steps

Be specific and actionable.`;

    const messages = [
      new SystemMessage(systemPrompt),
      new HumanMessage(`Create a detailed plan for: ${prompt}`)
    ];

    try {
      this.sendSSEEvent(res, 'start', 'Starting plan generation...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      this.sendSSEEvent(res, 'end', 'Plan generation completed');
      res.end();
    } catch (error) {
      console.error('Error generating plan:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Generate code from plan
   */
  async generateCode(plan, res, provider = null) {
    const llm = this.createLLM(provider, true, 'code-generation'); // Use code generation model

    const systemPrompt = `You are an elite frontend developer converting detailed plans into production-grade HTML prototypes. Create clean, semantic, and visually stunning web interfaces that match the quality of modern design tools.

## CRITICAL REQUIREMENTS:

### Code Quality Standards:
- Write clean, semantic HTML5 with proper document structure
- Use modern CSS with custom properties (CSS variables)
- Implement responsive design with mobile-first approach
- Follow accessibility best practices (ARIA labels, semantic elements)
- Use consistent naming conventions (BEM methodology preferred)
- Include proper meta tags and viewport configuration

### Design System Implementation:
- Typography: System fonts with proper hierarchy (2.5rem, 2rem, 1.5rem, 1.25rem, 1rem)
- Colors: Professional palette with primary (#2563eb), secondary (#10b981), neutral grays
- Spacing: Consistent 8px grid system (0.5rem, 1rem, 1.5rem, 2rem, 3rem, 4rem)
- Components: Clean buttons, cards, forms with subtle shadows and proper states
- Layout: CSS Grid for page structure, Flexbox for components

### Code Structure:
- Properly formatted and indented HTML
- Organized CSS with logical grouping (reset, variables, layout, components)
- Minimal, efficient JavaScript for interactions
- Comments for complex sections
- No external dependencies unless absolutely necessary

### Plan Implementation:
- Follow the plan specifications exactly
- Implement all features and sections mentioned in the plan
- Maintain consistency with the planned design approach
- Include proper error handling and user feedback where specified

Return ONLY the complete HTML document with embedded CSS and JavaScript. No markdown formatting, no explanations, just clean production code.`;

    const messages = [
      new SystemMessage(systemPrompt),
      new HumanMessage(`Implement this plan as a complete HTML document:\n\n${plan}`)
    ];

    try {
      this.sendSSEEvent(res, 'start', 'Starting code generation...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      this.sendSSEEvent(res, 'end', 'Code generation completed');
      res.end();
    } catch (error) {
      console.error('Error generating code:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }
}

module.exports = new LLMServiceV3();
