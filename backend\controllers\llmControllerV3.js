const llmServiceV3 = require('../services/llmServiceV3');

/**
 * V3 LLM Controller - Clean implementation based on Readdy.ai approach
 * Focuses on accurate, targeted editing through sophisticated prompting
 */

/**
 * Generate intent from element interaction (Readdy.ai style)
 */
async function generateIntent(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { elementCode } = req.body;
    if (!elementCode) {
      return res.status(400).json({ error: 'Element code is required' });
    }

    const result = await llmServiceV3.generateIntent(elementCode);
    res.json(result);
  } catch (error) {
    console.error('Error in generateIntent:', error);
    next(error);
  }
}

/**
 * Generate complete HTML from prompt
 */
async function generateHTML(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prompt } = req.body;
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    console.log('🎯 [Controller] generateHTML received request');
    console.log('📏 Prompt length:', prompt.length);
    console.log('🔍 Contains plan data:', prompt.includes('DETAILED IMPLEMENTATION PLAN'));
    console.log('🔍 Contains sections:', prompt.includes('Implementation Requirements'));
    console.log('🔍 Contains features:', prompt.includes('Key Features'));

    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    await llmServiceV3.generateHTML(prompt, res);
  } catch (error) {
    console.error('Error in generateHTML:', error);
    next(error);
  }
}

/**
 * Edit existing HTML with targeted changes (Readdy.ai style)
 */
async function editHTML(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { htmlContent, prompt, elementSelector, conversationHistory } = req.body;
    if (!htmlContent || !prompt) {
      return res.status(400).json({
        error: 'HTML content and prompt are required'
      });
    }

    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    await llmServiceV3.editHTML(htmlContent, prompt, res, null, elementSelector, conversationHistory || []);
  } catch (error) {
    console.error('Error in editHTML:', error);
    next(error);
  }
}

/**
 * Step 1: Generate Intent from element click (like Readdy's /api/page_gen/generate_intent)
 */
async function generateIntent(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { elementCode, htmlContent, conversationHistory } = req.body;
    if (!elementCode || !htmlContent) {
      return res.status(400).json({
        error: 'Element code and HTML content are required'
      });
    }

    const result = await llmServiceV3.generateIntent(
      elementCode,
      htmlContent,
      conversationHistory || []
    );

    if (result.success) {
      res.json({
        code: 'OK',
        data: result.intent,
        meta: {
          time: Date.now(),
          request_id: `intent_${Date.now()}`,
          message: '',
          detail: null
        }
      });
    } else {
      res.status(500).json({
        code: 'ERROR',
        error: result.error,
        meta: {
          time: Date.now(),
          request_id: `intent_${Date.now()}`,
          message: 'Failed to generate intent',
          detail: result.error
        }
      });
    }
  } catch (error) {
    console.error('Error in generateIntent:', error);
    next(error);
  }
}

/**
 * Generate structured plan from prompt (for plan review page)
 */
async function generatePlan(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prompt, deviceType } = req.body;
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    const result = await llmServiceV3.generateStructuredPlan(prompt, deviceType);

    if (result.success) {
      res.json({ plan: result.plan });
    } else {
      res.status(500).json({ error: result.error });
    }
  } catch (error) {
    console.error('Error in generatePlan:', error);
    next(error);
  }
}

/**
 * Generate streaming plan (for chat)
 */
async function generateStreamingPlan(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prompt } = req.body;
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    await llmServiceV3.generatePlan(prompt, res);
  } catch (error) {
    console.error('Error in generateStreamingPlan:', error);
    next(error);
  }
}

/**
 * Generate code from plan
 */
async function generateCode(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { plan } = req.body;
    if (!plan) {
      return res.status(400).json({ error: 'Plan is required' });
    }

    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    await llmServiceV3.generateCode(plan, res);
  } catch (error) {
    console.error('Error in generateCode:', error);
    next(error);
  }
}

module.exports = {
  generateIntent,
  generateHTML,
  editHTML,
  generatePlan,
  generateStreamingPlan,
  generateCode
};
