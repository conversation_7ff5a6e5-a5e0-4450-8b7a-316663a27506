model_list:
  # DeepSeek Models
  - model_name: deepseek/deepseek-chat
    litellm_params:
      model: deepseek/deepseek-chat
      api_key: os.environ/DEEPSEEK_API_KEY
      
  - model_name: deepseek/deepseek-coder-v2
    litellm_params:
      model: deepseek/deepseek-coder-v2
      api_key: os.environ/DEEPSEEK_API_KEY
      
  - model_name: deepseek/deepseek-reasoner
    litellm_params:
      model: deepseek/deepseek-reasoner
      api_key: os.environ/DEEPSEEK_API_KEY

  # Qwen Models via OpenRouter
  - model_name: qwen/qwen-2.5-72b-instruct
    litellm_params:
      model: openrouter/qwen/qwen-2.5-72b-instruct
      api_key: os.environ/OPENROUTER_API_KEY
      api_base: https://openrouter.ai/api/v1
      
  - model_name: qwen/qwen-2.5-32b-instruct
    litellm_params:
      model: openrouter/qwen/qwen-2.5-32b-instruct
      api_key: os.environ/OPENROUTER_API_KEY
      api_base: https://openrouter.ai/api/v1

  # OpenAI Models (when available)
  - model_name: gpt-4o
    litellm_params:
      model: gpt-4o
      api_key: os.environ/OPENAI_API_KEY
      
  - model_name: gpt-4o-mini
    litellm_params:
      model: gpt-4o-mini
      api_key: os.environ/OPENAI_API_KEY

  # Anthropic Models (backup)
  - model_name: claude-3-5-sonnet-********
    litellm_params:
      model: claude-3-5-sonnet-********
      api_key: os.environ/ANTHROPIC_API_KEY

# General settings
general_settings:
  master_key: sk-1234 # Change this in production
  database_url: "sqlite:///litellm.db"
  
# Logging
litellm_settings:
  success_callback: ["langfuse"] # Optional: for tracking
  failure_callback: ["langfuse"]
  set_verbose: true
  json_logs: true

# Rate limiting and budgets (optional)
router_settings:
  routing_strategy: "least-busy" # Options: simple-shuffle, least-busy, usage-based-routing
  model_group_alias: 
    "reasoning": ["deepseek/deepseek-reasoner", "qwen/qwen-2.5-72b-instruct"]
    "coding": ["deepseek/deepseek-coder-v2", "gpt-4o"]
    "general": ["deepseek/deepseek-chat", "qwen/qwen-2.5-32b-instruct", "gpt-4o-mini"]
